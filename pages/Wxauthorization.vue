<template>
  <div v-show="!!size('m')" class="container">
    <!-- <van-button @click="changColorBlue" type="primary">蓝色#2F92EE</van-button>
          <van-button @click="changColorDefault" type="primary">紫色#8A4CFF</van-button>
          <van-button @click="changColorOrange" type="primary">橙色</van-button> -->

    <!-- <div v-if="$store.state.projectData.defaultHome && $store.state.projectData.defaultHome === '/defaultHome'"> -->

    <div class="bg-height bgColor">
      <!-- <img src="https://static.medsci.cn/public-image/ms-image/b5db7670-790c-11ec-92bc-af0e6edd4208_kv.png" alt=""> -->
      <img :src="backImage" alt="" />
    </div>

    <!-- <div v-else class="bgColor bgImg">
        <p class="welcome-font">WELCOME</p>
        <p class="welcome-font-h2">医讯达</p>
      </div> -->
    <div class="content">
      <h2 class="title">绑定账号</h2>
      <!-- <span class="tip">未注册的手机号验证成功后会为您自动创建账户</span> -->
      <div class="radiusSty mobileSty">
        <svg-icon icon-class="mobile" class="iconSty" />
        <van-field
          type="tel"
          v-model.number="phoneNumber"
          placeholder="请输入手机号"
          :clearable="true"
          maxlength="11"
        >
        </van-field>
      </div>

      <div class="flexSty">
        <div class="radiusSty mobileSty" style="width: 60%; margin-right: 3%">
          <svg-icon icon-class="verificationCode" class="iconSty" />
          <van-field
            v-model="validCode"
            maxlength="6"
            placeholder="请输入验证码"
          />
        </div>
        <van-button
          plain
          type="info"
          style="flex: 1; vertical-align: middle"
          class="radiusSty txtSize"
          :class="{ 'disabled-valid-btn': validActive }"
          @click="getValidCode"
        >
          <span v-show="!validActive" class="fnColor">发送验证码</span>
          <span v-show="validActive">已发送 {{ validBtnSecond }}s</span>
        </van-button>
      </div>

      <div class="nc-wrap" ref="ncWrap" v-if="showSlideDom" :key="slideKey">
        <div id="nc"></div>
      </div>

      <!-- 知情同意书勾选框 -->
      <div class="consent-check-wrapper" v-if="consentText">
        <van-checkbox v-model="consentChecked" icon-size="16px">
          <span class="consent-text">
            我已阅读并同意
            <span @click="showConsentDialog = true" class="consent-link">《知情同意书》</span>
          </span>
        </van-checkbox>
      </div>

      <van-button
        @click="wxLogin(false, sign)"
        :disabled="!canConfirm"
        :class="{
          'btnSty disabled-btn': !canConfirm,
          'btnSty bgColor active-btn': canConfirm,
        }"
        >确 认</van-button
      >

      <!-- 知情同意书弹窗 -->
      <van-dialog
        v-model="showConsentDialog"
        title="知情同意书"
        :show-cancel-button="false"
        confirm-button-text="我已阅读并同意"
        :confirm-button-color="'var(--button-color-theme)'"
        class="consent-dialog"
        @confirm="handleConsentAgree"
      >
        <div class="consent-content" v-html="escapeHtml(consentText)"></div>
      </van-dialog>
    </div>
  </div>
</template>

<script>
import Mixin from "@/mixins/login/index";
import { escape2Html } from "@/utils/index";
export default {
  name: "Login",
  data() {
    return {
      sign: "",
      showConsentDialog: false,
      consentText: "",
      consentChecked: false,
      consentDetail: {}, // 存储知情同意书详细信息
    };
  },
  computed: {
    backImage() {
      return (
        this.$store.state.projectData.h5BackgroundUrl ||
        "https://static.medsci.cn/public-image/ms-image/b1ca9b60-59a3-11ed-b66b-937b834e3ef9_default.png"
      );
    },
    // 控制确认按钮是否可点击
    canConfirm() {
      // 基础条件：验证码已发送
      const basicCondition = this.validActive;
      // 如果有知情同意书内容，则必须勾选同意
      const consentCondition = this.consentText ? this.consentChecked : true;
      return basicCondition && consentCondition;
    },
  },
  methods: {
    // 获取知情同意书内容
    async getConsentText() {
      try {
        const result = await this.$api.user.getAgreeText();
        if (result.data && result.data.content) {
          this.consentText = result.data.content;
          this.consentDetail = result.data; // 保存完整的详细信息
        }
      } catch (err) {
        console.error('获取知情同意书内容失败:', err);
      }
    },

    // 处理知情同意书同意操作
    async handleConsentAgree() {
      try {
        // 调用更新同意状态的接口
        const data = {
          status: 1,
          companyId: this.consentDetail.id,
        };
        await this.$api.user.updateAgreeStatus(data);

        // 更新store中的同意状态
        await this.$store.dispatch("user/getAgreeBook");
        this.$store.commit("user/SET_AGREE_STATUS", 1);

        // 自动勾选同意框
        this.consentChecked = true;

        // 关闭弹窗
        this.showConsentDialog = false;

        this.$toast('已确认知情同意书');
      } catch (err) {
        console.error('更新知情同意书状态失败:', err);
        this.$toast('操作失败，请重试');
      }
    },

    // HTML转义处理
    escapeHtml(str) {
      if (!str) return '';
      return escape2Html(str);
    },
  },
  async created() {
    this.sign = this.$store.state.user.wxsign;
    // 获取知情同意书内容
    await this.getConsentText();
  },
  async mounted(){
    this.loadTencentCaptcha()
  //   let urlData = Object.fromEntries(window.location.search.slice(1).split('&').map(item=>item.split('=')))
  //   this.code = urlData.code
  //   this.state = urlData.state
  //   const res = await this.$api.user.getWxLogin(this.code,this.state)
  //   // this.logText = res
  //   if(res.status===200){
  //      if(Object.keys(res.data).length===1){
  //       // 绑定手机号授权
  //       //  this.$store.dispatch('auth/SET_AUTH', res.data.sign)
  //        this.sign = res.data.sign
  //      }else{
  //       // 用户已授权，登录操作
  //       // 设置用户信息
  //       this.$store.commit('user/SET_USERINFO', res.data)
  //       // 存储token
  //       this.$store.commit('user/SET_TOKEN', res.data.token.accessToken)
  //       setUserInfo(this.$cookies, res.data)
  //       setToken(this.$cookies, res.data.token.accessToken)
  //       // 重置埋点信息
  //       const appId = this.$store.state.projectData.projectId
  //       const channel = this.$store.state.platform === 'm' ? 'yxd_h5' : 'yxd_pc'
  //       const id = this.$store.state.user.userinfo.userId
  //       const token = this.$store.state.user.token
  //       window.MsStatis.init(appId, channel, id, token)
  //       console.log(this.$store.state.user.userinfo);
  //       if(this.$store.state.user.userinfo.integralNum){
  //         this.$toast(`恭喜您，获得${this.$store.state.user.userinfo.integralNum}梅花！`)
  //       }
  //       if (
  //         this.$store.state.projectData.defaultHome &&
  //         this.$store.state.projectData.defaultHome === '/defaultHome'
  //       ) {
  //         this.$router.push('/defaultHome')
  //       } else {
  //         this.$router.push({
  //           path: this.redirect || '/',
  //           query: this.otherQuery
  //         })
  //       }
  //      }
  //   }else{
  //     console.log(res.message);
  //   }
  },
  mixins: [Mixin],
};
</script>
<style src="./../styles/index.scss" scoped lang="scss"></style>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 3rem;
}

/*.fontColor {*/
/*  color: #2f92ee;*/
/*}*/

.txtSize {
  margin-top: 0.4rem;
  font-size: 0.24rem;
}

.must-check {
  display: flex;
}
.agree-text {
  margin-left: 0.12rem;
  font-size: 0.24rem;
  color: #999;
}
.user-xy {
  color: #2f92ee;
}

.content {
  position: relative;
  border: 1 solid #fff;
  margin: -0.4rem auto 0;
  background: #fff;
  border-top-left-radius: 0.4rem !important;
  border-top-right-radius: 0.4rem !important;
  z-index: 1001;
  box-sizing: border-box;
  padding: 0.56rem 0.6rem 0;

  &::v-deep {
    .van-field {
      padding-left: 0.16rem;
    }
  }
  .title {
    font-size: 0.45rem;
    font-family: PingFangSC-Medium ,sans-serif;
    font-weight: 600;
    color: #333333;
    margin-bottom: 0.25em;
  }
  .wxAuthorization {
    margin-top: 10px;
    text-align: center;
  }
}

.welcome-font {
  /* font-family: "PingFangSCSemibold"; */
  font-size: 0.8rem;
  color: #fff;
}
.new-welcome-font {
  padding-top: 1.27rem;
  color: #fff;
  text-align: center;
  font-size: 0.6rem;
}

.welcome-font-h2 {
  font-family: "PingFangSC" ,sans-serif;
  font-size: 0.56rem;
  color: #fff;
  font-weight: 600;
}
.new-welcome-font-h2 {
  color: #fff;
  font-size: 0.56rem;
  font-weight: 600;
  text-align: center;
}

.bgImg {
  background-image: url("../assets/images/welcomeBg.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 6rem;
  width: 100%;
  box-sizing: border-box;
  padding-top: 1.77rem;
  padding-left: 0.75rem;
}

.newbgImg {
  background-image: url("https://static.medsci.cn/public-image/ms-image/3ecf8aa0-8d7d-11ec-bca5-7f892b5df5d6_bgc.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 4rem;
  width: 100%;
  box-sizing: border-box;
}

.tip {
  font-size: 0.24rem;
  color: #999;
  line-height: 0.33rem;
}

.title {
  font-size: 0.44rem;
  color: #333;
  line-height: 0.62rem;
  font-weight: 500;
}

.radiusSty {
  border: 1px solid #d8d8d8;
  border-radius: 0.12rem;
  margin-top: 0.4rem;
  height: 0.8rem;
}

.mobileSty {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.iconSty {
  margin-left: 0.26rem;
  font-size: 0.48rem;
  /* width: .4rem;
  height: .4rem; */
}

.flexSty {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  margin-bottom: 0.25rem;
}

.btnSty {
  width: 100%;
  height: 0.8rem;
  opacity: 1;
  border-radius: 0.12rem;
  margin-top: 0.4rem;
  font-size: 0.34rem;
}

.disabled-btn {
  background: #eee;
  color: #ccc;
}
.active-btn {
  color: #fff;
}

.disabled-valid-btn {
  background-color: #eee;
  color: #999;
  border: none;
  &::v-deep {
    .van-button__text {
      color: #999;
    }
  }
}

.nc-wrap {
  height: 32px;
  width: 100%;
  margin-top: 0.4rem;
  ::v-deep .nc-container {
    .nc_scale {
      span {
        height: 34px;
      }
    }
  }
}
#nc {
  width: 100%;
}
.fnColor {
  color: var(--font-color-theme);
}
.bgColor {
  background-color: var(--button-color-theme);
}

/* 知情同意书勾选框样式 */
.consent-check-wrapper {
  margin-top: 0.4rem;

  .consent-text {
    font-size: 0.24rem;
    color: #999;
    line-height: 0.33rem;
  }

  .consent-link {
    color: var(--button-color-theme);
    text-decoration: underline;
    cursor: pointer;
  }

  &::v-deep .van-checkbox__icon--checked .van-icon {
    background-color: var(--button-color-theme) !important;
    border-color: var(--button-color-theme) !important;
  }
}

/* 知情同意书弹窗样式 */
::v-deep .consent-dialog {
  .van-dialog {
    max-height: 80vh;
    border-radius: 0.16rem;
  }

  .van-dialog__header {
    padding: 0.4rem 0.32rem 0.2rem;
    font-size: 0.36rem;
    font-weight: 600;
    color: #333;
  }

  .van-dialog__content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0 0.32rem;
  }

  .van-dialog__footer {
    padding: 0.2rem 0.32rem 0.4rem;
  }

  .van-button--primary {
    background-color: var(--button-color-theme);
    border-color: var(--button-color-theme);
  }
}

/* 知情同意书内容样式 */
.consent-content {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #666;
  text-align: justify;

  ::v-deep {
    h1, h2, h3, h4, h5, h6 {
      font-size: 0.32rem;
      font-weight: 600;
      color: #333;
      margin: 0.2rem 0 0.1rem 0;
    }

    p {
      margin-bottom: 0.16rem;
    }

    a {
      color: var(--button-color-theme);
    }
  }
}
</style>
