<template>
  <div class="chat-container">
    <van-pull-refresh
      v-model="loading"
      @refresh="onRefresh"
      :disabled="isdisabled"
      id="scrollbar"
    >
      <div class="chat-messages">
        <!-- 空状态提示 -->
        <div v-if="messageList.length === 0 && !loading" class="empty-state">
          <div class="empty-icon">💬</div>
          <div class="empty-text">暂无聊天记录</div>
          <div class="empty-subtext">开始您的第一次对话吧</div>
        </div>

        <div
          v-for="(item, index) in messageList"
          :key="index"
          class="chat-message"
          :class="{
            'message-sent': item.type == '1',
            'message-received': item.type == '2',
          }"
        >
          <img
            src="https://img.medsci.cn/202411/0295bd464bb14aff8485caac60f3e23c-riASLf00JIPZ.png"
            v-if="item.type == '2'"
            class="avator_bot"
          />
             <div class="msg" v-if="item.type == '2'&&!item.loading">
               <VueMarkdown :source="item.answer" :html="false"></VueMarkdown>
             </div>
            <div class="msg" v-if="item.type == '2'&&item.loading">
            <van-loading color="#1989fa" />
          </div>
          <!-- <div class="msg" v-if="item.type == '2'" v-html="item.answer"></div> -->
             <!-- <v-md-preview  v-if="item.type == '1'" class="msg-user" :text="item.answer"></v-md-preview> -->
          <div
            class="msg-user"
            v-if="item.type == '1'"
            v-html="item.answer"
          ></div>
        
          <img
            :src="getUserAvatar()"
            v-if="item.type == '1'"
            class="avator_user"
             @error="handleImageError"
          />
        </div>
      </div>
    </van-pull-refresh>
    <div class="chat-input">
      <input
        type="text"
        enterkeyhint="send"
        v-model="inputMessage"
        @keyup.enter="sendMessage"
        :placeholder="contextDisable ? '加载中' : '请输入您的问题，回车键发送'"
        :disabled="contextDisable"
      />
      <!-- <button @click="sendMessage">Send</button> -->
    </div>
  </div>
</template>

<script>
import { nextTick } from "vue";
import VueMarkdown from 'vue-markdown';

export default {
  layout: 'nav',
  head() {
    return {
      title: `AI智能问答 - ${this.globalTitle || 'AI助手'}`
    }
  },
  components: {
    VueMarkdown
  },
  data() {
    return {
      inputMessage: "",
      messageTp: "",
      messageList: [],
      answer: "",
      conversationId: "",
      typingQueue: [],
      isTyping: false,
      contextDisable: false,
      loading: false,
      isdisabled: false,
      params: {
        pageIndex: 1,
        pageSize: 50,
      },
    }
  },
  async mounted() {
    await this.init();

    // 初始化完成后滚动到底部
    this.scrollToBottom();

    // PC端隐藏页脚
    this.hidePcFooter();
  },
  methods: {
    // 查询聊天记录
    async init() {
      try {
        let res = await this.$api.ai.chatPage(this.params);
        if (res.data && Array.isArray(res.data)) {
          // res.data 直接就是数组
          const dataList = res.data;

          if (dataList.length > 0) {
            // 有数据时设置会话ID
            this.conversationId = dataList[0].conversationId;

            if (this.params.pageIndex > 1) {
              this.messageList = dataList.concat(this.messageList);
            } else {
              this.messageList = this.messageList.concat(dataList);
            }
          }

          // 如果返回的数据少于pageSize，说明已经是最后一页
          if (dataList.length < this.params.pageSize) {
            this.isdisabled = true;
            this.loading = false;
          }
        } else {
          // 如果没有数据或数据格式不正确
          this.isdisabled = true;
          this.loading = false;
        }
      } catch (error) {
        // 设置默认值，确保页面正常显示
        this.isdisabled = true;
        this.loading = false;
      }
    },
    // 默认头像
    handleImageError(event) {
      event.target.src = 'https://static.medsci.cn/public-image/ms-image/75b5af80-40ba-11ee-8e90-b931ae271702_user.png';
    },
    // 下拉查询列表
    async onRefresh() {
      this.loading = true;

      // 如果已经禁用了刷新，直接返回
      if (this.isdisabled) {
        this.loading = false;
        return;
      }

      this.params.pageIndex++;
      await this.init();
      this.loading = false;
    },
    sendMessage() {
      if (this.inputMessage) {
        this.contextDisable = true;
        const userlist = [
          {
            chatName: this.$store.state.user && this.$store.state.user.userinfo ? this.$store.state.user.userinfo.userName || '用户' : '用户',
            type: 1,
            answer: this.inputMessage.replace(/\n/g, "<br/>"),
          },
          {
            chatName: "medsciAI",
            conversationId: this.conversationId,
            loading: true,
            type: 2,
            answer: "",
          },
        ];
        this.messageList.push(...userlist);

        // 滚动到底部
        this.scrollToBottom();

        this.messageTp = this.inputMessage;
        this.inputMessage = ""; // 清空用户输入
        this.getAnswer();
      }
    },
    // AI 回答处理
    async getAnswer() {
      const controller = new AbortController();
      this.typingQueue = [];
      this.answer = "";

      // 构建对话接口 URL
      let url = "/apis/yxd/web/ai/chat"

      let params = {
        query: this.messageTp,
        conversationId: this.conversationId,
      };

      try {
        const { fetchEventSource } = await import('@microsoft/fetch-event-source');

        await fetchEventSource(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
            Authorization: this.$store.state.user && this.$store.state.user.token
              ? this.$store.state.user.token
              : "",
          },
          body: JSON.stringify(params),
          signal: controller.signal,
          onopen: (res) => {
            if (!res.ok) {
              throw new Error("连接失败");
            }
          },
          onmessage: (event) => {
            const data = JSON.parse(event.data);
            if (data.error) {
              controller.abort();
              this.handleMessage(data.error);
              this.handleChatStatus();
              return;
            }
            if (data.event === "message") {
              // 更新会话ID，确保下次对话能够延续
              if (data.conversation_id) {
                this.conversationId = data.conversation_id;
              }
              this.typingQueue.push(data.answer);
              if (!this.isTyping) {
                this.processTypingQueue();
              }
            }
            if (data.event === "workflow_finished") {
              this.handleChatStatus();
              controller.abort();
            }
          },
          onerror: () => {
            this.handleChatStatus();
            this.handleMessage("访问太火爆了！休息下，请稍后再试！");
            controller.abort();
          },
        });
      } catch (error) {
        this.handleChatStatus();
        this.handleMessage("访问太火爆了！休息下，请稍后再试！");
        controller.abort();
      }
    },
    handleChatStatus() {
      this.contextDisable = false;
      const length = this.messageList.length;
      this.messageList[length - 1]["loading"] = false;

      // 聊天状态改变后滚动到底部
      this.scrollToBottom();
    },
    processTypingQueue() {
      if (this.typingQueue.length === 0) {
        this.isTyping = false;
        // 打字队列处理完成，最后滚动到底部
        this.scrollToBottom();
        return;
      }

      this.isTyping = true;
      const text = this.typingQueue.shift();
      this.simulateTyping(text).then(() => {
        this.processTypingQueue();
      });
    },
    simulateTyping(text) {
      return new Promise((resolve) => {
        let index = 0;
        let lastScrollTime = 0;
        const scrollThrottle = 100; // 限制滚动频率为每100ms一次

        const interval = setInterval(() => {
          if (index < text.length) {
            this.answer += text[index++];
            this.handleMessage(this.answer);

            // 节流滚动，避免过于频繁的滚动影响性能
            const now = Date.now();
            if (now - lastScrollTime > scrollThrottle || index === text.length) {
              this.scrollToBottom();
              lastScrollTime = now;
            }
          } else {
            clearInterval(interval);
            // 确保最后滚动到底部
            this.scrollToBottom();
            resolve();
          }
        }, 30);
      });
    },
    handleMessage(answer) {
      const length = this.messageList.length;
      this.messageList[length - 1].answer = answer;
    },

    // 滚动到底部的通用方法
    scrollToBottom(smooth = false) {
      nextTick(() => {
        try {
          // 获取滚动容器
          const scrollContainer = document.getElementsByClassName("van-pull-refresh__track")[0];

          if (scrollContainer) {
            const scrollHeight = scrollContainer.scrollHeight;
            const clientHeight = scrollContainer.clientHeight;
            const maxScrollTop = scrollHeight - clientHeight;

            if (smooth && scrollContainer.scrollTo && 'scrollBehavior' in document.documentElement.style) {
              // 平滑滚动（仅在支持的浏览器中使用）
              scrollContainer.scrollTo({
                top: maxScrollTop,
                behavior: 'smooth'
              });
            } else {
              // 立即滚动到底部
              scrollContainer.scrollTop = maxScrollTop;
            }

            // 使用 requestAnimationFrame 确保滚动生效
            requestAnimationFrame(() => {
              scrollContainer.scrollTop = maxScrollTop;

              // 移动端额外处理，确保在iOS Safari等浏览器中正常工作
              if (this.isMobile()) {
                setTimeout(() => {
                  scrollContainer.scrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;
                }, 50);
              } else {
                // PC端双重保险
                setTimeout(() => {
                  scrollContainer.scrollTop = maxScrollTop;
                }, 10);
              }
            });
          }
        } catch (error) {
          console.warn('自动滚动失败:', error);
        }
      });
    },

    // 检测是否为移动端
    isMobile() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
             window.innerWidth <= 768;
    },

    // 获取用户头像
    getUserAvatar() {
      if (this.$store.state.user && this.$store.state.user.userinfo && this.$store.state.user.userinfo.avatar) {
        return this.$store.state.user.userinfo.avatar;
      }
      // 返回默认头像
      return 'https://static.medsci.cn/public-image/ms-image/75b5af80-40ba-11ee-8e90-b931ae271702_user.png';
    },

    // PC端隐藏页脚
    hidePcFooter() {
      if (process.client && this.size('pc')) {
        // 使用多种方式查找并隐藏页脚
        const selectors = [
          '.pc-footer-wrapper',
          '.pc-footer',
          'footer',
          '[class*="footer"]'
        ];

        const hideFooter = () => {
          selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              el.style.display = 'none';
              el.style.visibility = 'hidden';
              el.style.height = '0';
              el.style.overflow = 'hidden';
            });
          });
        };

        // 立即执行
        hideFooter();

        // 延迟执行，确保DOM完全加载
        setTimeout(hideFooter, 100);
        setTimeout(hideFooter, 500);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* Styles remain the same as in the previous example */
.chat-container {
  margin: auto;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  height: 100%;
  :deep(.van-pull-refresh__track) {
    overflow: auto;
    /* 改善移动端滚动体验 */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  :deep(.van-pull-refresh) {
    // 移动端减去 header 高度 (0.88rem ≈ 44px)
    height: calc(100vh - 44px);
  }
}

/* 基础样式优先定义，PC端样式将在文件末尾覆盖 */

.chat-messages {
  overflow-y: auto;
  padding: 20px 14px 78px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

.chat-message {
  margin: 5px 0;
  border-radius: 5px;
}

.msg {
  max-width: 275px;
  padding: 15px;
  background: #ffffff;
  border-radius: 0 7.5px 7.5px 7.5px;
  font-size: 14px;
  line-height: 18px;
  word-wrap: break-word;
  word-break: break-word;

  :deep(.vue-markdown) {
    padding: 0;
    font-size: 14px;
    line-height: 1.6;
    width: 100%;
  }

  :deep(.vue-markdown p) {
    margin: 8px 0;
    display: block;
    width: 100%;
    line-height: 1.6;
  }

  :deep(.vue-markdown p:first-child) {
    margin-top: 0;
  }

  :deep(.vue-markdown p:last-child) {
    margin-bottom: 0;
  }

  :deep(.vue-markdown h1, .vue-markdown h2, .vue-markdown h3, .vue-markdown h4, .vue-markdown h5, .vue-markdown h6) {
    margin: 12px 0 8px 0;
    font-weight: bold;
    display: block;
    width: 100%;
  }

  :deep(.vue-markdown ul, .vue-markdown ol) {
    margin: 8px 0;
    padding-left: 20px;
    display: block;
    width: 100%;
  }

  :deep(.vue-markdown li) {
    margin: 4px 0;
    display: list-item;
  }

  :deep(.vue-markdown pre) {
    white-space: pre-wrap;
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    margin: 8px 0;
    display: block;
    width: 100%;
    overflow-x: auto;
  }

  :deep(.vue-markdown code) {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: 'Courier New', Courier, monospace;
  }

  :deep(.vue-markdown blockquote) {
    border-left: 4px solid #ddd;
    padding-left: 12px;
    margin: 8px 0;
    color: #666;
    display: block;
    width: 100%;
  }

  :deep(.vue-markdown strong) {
    font-weight: bold;
  }

  :deep(.vue-markdown em) {
    font-style: italic;
  }
}

.msg-user {
  max-width: 275px;
  padding: 15px;
  background: #a9d5f8;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-radius: 7.5px 0 7.5px 7.5px;
  text-align: left;
  font-size: 14px;
  line-height: 18px;
}

.avator_bot,
.avator_user {
  width: 50px;
  height: 50px;
  background: #ffffff;
  border-radius: 50%;
}

.avator_bot {
  margin-right: 11px;
}

.avator_user {
  margin-left: 11px;
}

.message-sent {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 22.5px;
}

.message-received {
  text-align: left;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 22.5px;
}

.chat-input {
  display: flex;
  position: absolute;
  bottom: 0;
  padding: 10px;
  width: 100%;
  height: 79px;
  background: #f7f7f7;
}

.chat-input input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 7px 7px 7px 7px;
  background: #ededed;
  width: 100%;
  height: 49.5px;
}

.chat-input button {
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  background-color: #409eff;
  color: white;
  cursor: pointer;
}

.chat-input button:hover {
  background-color: #66b1ff;
}

/* PC 端样式 - 放在最后确保优先级 */
@media (min-width: 768px) {
  /* 隐藏PC端页脚 - 使用多种选择器确保生效 */
  :deep(.pc-footer-wrapper),
  :deep(.pc-footer),
  :deep(footer),
  :deep([class*="footer"]) {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    overflow: hidden !important;
  }

  .chat-container {
    max-width: 1180px !important;
    margin: 0 auto !important;
    height: calc(100vh - 64px) !important; /* 减去header高度64px，无需考虑页脚 */
    border-radius: 0 !important;
    box-shadow: none !important;

    :deep(.van-pull-refresh) {
      height: 100% !important;
      border-radius: 0 !important;
    }
  }

  .chat-messages {
    padding: 20px 40px 120px !important;

    /* PC 端滚动条美化 */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .chat-input {
    position: absolute !important;
    bottom: 20px !important;
    left: 40px !important;
    right: 40px !important;
    width: calc(100% - 80px) !important;
    padding: 20px 0 !important;
    background: #f7f7f7 !important;
    box-sizing: border-box !important;
    z-index: 1000 !important;
  }

  /* 添加底部遮罩，覆盖输入框下方的20px空白区域 */
  .chat-input::after {
    content: '' !important;
    position: absolute !important;
    bottom: -20px !important;
    left: -40px !important;
    right: -40px !important;
    height: 20px !important;
    background: #f7f7f7 !important;
    z-index: 999 !important;
  }

  .chat-input input {
    height: 58px !important;
    font-size: 16px !important;
    padding: 15px 20px !important;
    border: 1px solid #e1e8ed !important;
    background: #fff !important;

    &:focus {
      outline: none !important;
      border-color: #409eff !important;
      box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2) !important;
    }
  }

  /* PC 端消息框宽度调整 */
  .msg {
    max-width: 800px !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    padding: 20px !important;
  }

  .msg-user {
    max-width: 700px !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    padding: 20px !important;
  }

  /* 空状态在 PC 端的样式 */
  .empty-state {
    padding: 100px 20px !important;
  }

  .empty-icon {
    font-size: 64px !important;
  }

  .empty-text {
    font-size: 18px !important;
  }

  .empty-subtext {
    font-size: 16px !important;
  }

  /* 滚动条美化 */
  :deep(.van-pull-refresh__track) {
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}
</style>

<!-- 全局样式，用于隐藏PC端页脚 -->
<style lang="scss">
@media (min-width: 768px) {
  .pc-footer-wrapper {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    overflow: hidden !important;
  }
}
</style>