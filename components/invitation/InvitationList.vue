<template>
  <div class="invitation-list-container">
    <!-- 邀请项 -->
    <div
      v-for="item in list"
      :key="item.id"
      class="invitation-item"
      @click="$emit('item-click', item)"
    >
      <!-- 用户头像 -->
      <div class="avatar-section">
        <van-image
          width="0.8rem"
          height="0.8rem"
          round
          :src="item.avatar || defaultAvatar"
          :error-icon="'user-o'"
        />
      </div>

      <!-- 用户信息区域 -->
      <div class="info-section">
        <div class="user-name">{{ item.userName || '未知用户' }}</div>
        <div class="time-info">
          <div class="register-time">
            注册时间：{{ item.registerTime }}
          </div>
          <div v-if="item.completeTime" class="complete-time">
            完成时间：{{ item.completeTime }}
          </div>
        </div>
      </div>

      <!-- 状态标签 -->
      <div class="status-section">
        <div
          class="status-badge"
          :class="getStatusClass(item.status)"
        >
          {{ getStatusText(item.status) }}
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="list.length === 0" class="empty-container">
      <div class="empty-icon">{{ isLoading ? '⏳' : '📭' }}</div>
      <div class="empty-text">{{ emptyText }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InvitationList',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    emptyText: {
      type: String,
      default: '暂无邀请数据了'
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defaultAvatar: 'https://static.medsci.cn/public-image/ms-image/90246d70-bed5-11eb-b319-cd51f388f4e7_user.png'
    };
  },
  methods: {
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'completed': 'status-success',
        'incomplete': 'status-warning',
        'pending': 'status-info'
      };
      return statusMap[status] || 'status-default';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'completed': '已填写',
        'incomplete': '未填写',
        'pending': '待处理'
      };
      return statusMap[status] || '未知';
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = this.defaultAvatar;
    }
  }
};
</script>

<style scoped>
.invitation-list-container {
  width: 100%;
  padding: 0 0.3rem;
}

/* 移动端卡片样式 */
.invitation-item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 0.3rem;
  margin-bottom: 0.2rem;
  border-radius: 0.12rem;
  box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.invitation-item:active {
  background: #f8f8f8;
  transform: scale(0.98);
}

.avatar-section {
  margin-right: 0.26rem;
  flex-shrink: 0;
}

.info-section {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 0.32rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-info {
  font-size: 0.24rem;
  color: #999;
  line-height: 1.4;
}

.register-time,
.complete-time {
  margin-bottom: 0.05rem;
}

.status-section {
  flex-shrink: 0;
  margin-left: 0.2rem;
}

.status-badge {
  padding: 0.08rem 0.16rem;
  border-radius: 0.08rem;
  font-size: 0.24rem;
  font-weight: 500;
  text-align: center;
  min-width: 0.8rem;
}

/* 状态颜色 */
.status-success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-warning {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-info {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-default {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48PX;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 14PX;
  line-height: 1.5;
}
</style>
