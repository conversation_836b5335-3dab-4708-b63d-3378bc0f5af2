import { validPhone } from "@/utils/validate";
import configs from "@/configs/app";
import { setToken, setUserInfo } from "@/utils/auth";
export default {
  data() {
    return {
      getText: "获取验证码",
      phoneNumber: "",
      validCode: "",
      checked: false,
      path: {
        secret: "/privacy-policy",
        agreement: "/informed-consent",
      },
      showSlideDom: false,
      validBtnSecond: 60,
      validActive: false,
      type: "get",
      slideKey: 0,
      redirect: "",
      otherQuery: "",
      taskList: [],
      appid: "",
      code: "",
      state: "",
      randstr:"",
      ticket:"",
      CaptchaAppId:""
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
        }
      },
      immediate: true,
    },
  },
  computed: {
    disabled() {
      if (this.phoneNumber && this.validCode.trim() && this.checked)
        return false;
      return true;
    },
  },

  async mounted() {
    this.CaptchaAppId = this.$store.state.NoImgCaptchaAppld
    // 每次进入页面 需要重置阿里滑块
    // window.ic = null;

    // 当阿里滑块验证出错时（比如频繁调用时），reload当前页面，为了防止用户再次输入手机号造成不好的体验，从localStorage中读取
    // const phone = window.localStorage.getItem("yxd2.0_ali_slide");
    // if (phone) {
    //   this.phoneNumber = phone;
    //   window.localStorage.removeItem("yxd2.0_ali_slide");
    // }
  },
  methods: {
    openDetail(params) {
        window.open("https://www.medsci.cn/agreement/25", "_blank");
    },
    async handleLogin(disabled = false) {
      if (disabled) {
        console.log("登录信息不完整");
        return;
      }
      const data = {
        mobile: this.phoneNumber,
        code: this.validCode.trim(),
        requestSource: configs.source,
        loginType: "",
        loginIp: configs.ip,
        sourceUserId:localStorage.getItem("inviteUserId"),
        sourceAccountType:localStorage.getItem("inviteAccountType"),
        sourceFrom:this.$route.query?.sourceFrom||''
      };
      try {
        // 调用store中的登录接口
        await this.$store.dispatch("user/login", {
          userInfo: data,
          host: window.location.host,
        });

        // 重置埋点信息
        const appId = this.$store.state.projectData.projectId;
        const channel =
          this.$store.state.platform === "m" ? "yxd_h5" : "yxd_pc";
        const id = this.$store.state.user.userinfo.userId;
        const token = this.$store.state.user.token;
        window.MsStatis.init(appId, channel, id, token);
        console.log(this.$store.state.user.userinfo);
        if (this.$store.state.user.userinfo.integralNum) {
          this.$toast(
            `恭喜您，获得${this.$store.state.user.userinfo.integralNum}梅花！`
          );
        }
        if (
          this.$store.state.projectData.defaultHome &&
          this.$store.state.projectData.defaultHome === "/defaultHome"
        ) {
          this.$router.push("/defaultHome");
        } else {
          this.$router.push({
            path: this.redirect || "/",
            query: this.otherQuery,
          });
        }
      } catch (e) {
        if (e.message.indexOf("3次") !== -1) {
          if (window.ic) {
            window.ic.reset();
          }
          this.type = "clear";
          this.aliSlideValide();
        }
      }
    },

    async wxLogin(disabled = false, sign) {
      if (disabled) {
        console.log("登录信息不完整");
        return;
      }
      const data = {
        mobile: this.phoneNumber,
        code: this.validCode.trim(),
        // requestSource: configs.source,
        // loginType: '',
        // loginIp: configs.ip
        projectId: this.$store.state.projectData.projectId,
        sign,
      };
      try {
        // 调用store中的登录接口
        await this.$store.dispatch("user/login", {
          userInfo: data,
          host: window.location.host,
        });

        // 重置埋点信息
        const appId = this.$store.state.projectData.projectId;
        const channel =
          this.$store.state.platform === "m" ? "yxd_h5" : "yxd_pc";
        const id = this.$store.state.user.userinfo.userId;
        const token = this.$store.state.user.token;
        window.MsStatis.init(appId, channel, id, token);
        console.log(this.$store.state.user.userinfo);
        if (this.$store.state.user.userinfo.integralNum) {
          this.$toast(
            `恭喜您，获得${this.$store.state.user.userinfo.integralNum}梅花！`
          );
        }
        if (
          this.$store.state.projectData.defaultHome &&
          this.$store.state.projectData.defaultHome === "/defaultHome"
        ) {
          this.$router.push("/defaultHome");
        } else {
          this.$router.push({
            path: this.redirect || "/",
            query: this.otherQuery,
          });
        }
      } catch (e) {
        if (e.message.indexOf("3次") !== -1) {
          if (window.ic) {
            window.ic.reset();
          }
          this.type = "clear";
          this.aliSlideValide();
        }
      }
    },

    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
     //动态引入腾讯云验证码 JS
     loadTencentCaptcha () {
      return new Promise((resolve, reject) => {
      if (window.TencentCaptcha) {
        resolve()
      } else {
        const RSAUtils = document.createElement('script')
        RSAUtils.type = 'text/javascript'
        RSAUtils.src = 'https://turing.captcha.qcloud.com/TCaptcha.js'
        document.body.appendChild(RSAUtils)
        RSAUtils.onload = () => {
        setTimeout(() => {
          resolve()
        }, 10)
        }
      }
      })
    },
    //腾讯云无感登录回调
    callback (res) {
      console.log('callback:', res)
      // 验证通过后调用登录接口
      if (res.ticket) {
        if (res.errorCode) {
          console.log('error')
        }else{
          this.ticket =  res.ticket
          this.randstr = res.randstr
          // this.sendSms();
          // this.getCodeCount()
          this.sendCode()
        }
      }
    },
    //获取ticket和randStr后，调用获取验证码接口和倒计时方法
    sendCode(){
      const { projectCode, projectId } = this.$store.state.projectData;
      let slideParams={
        phoneNumber: this.phoneNumber,
        projectId,
        ticket:this.ticket,
        randstr:this.randstr,
        channel: 1,
        type:1,
        ip:"",
      }
           
      // this.$api.user.sendMessage(slideParams, window.location.host); 
      this.$api.user.sendMessage(slideParams, window.location.host).then((res) => {
          console.log(res, 'res')
          if (res.status == 200) {
            this.validActive = true;
            this.countDown();
          }
          // if (res.data && res.data.bizCode && [400, 800, 900].includes(Number(res.data.bizCode))) {
          //   // const captchas = new window.TencentCaptcha(this.CaptchaAppId, this.callback, options)
          //   // captchas.show()
          // //   this.codeSlideShow = true
          // //   document.getElementById('codeValidate').innerHTML = ''
          // //   const ncoption = {
          // //     // 声明滑动验证需要渲染的目标ID。
          // //     renderTo: 'codeValidate',
          // //   }
          // //   // 唤醒二次验证（滑动验证码）
          // //   // console.log(this.windowNc, 'this.windowNc')
          // //   this.windowNc.getNC(ncoption)
          // }
          if (res.status !=200) {
          this.$message({
            message: res.message,
            type: 'warning',
            customClass: 'msg-wrap',
          })
          }
        })
    },
    //获取腾讯云无感登录ticket、randStr
    getTicket(){
      const options = {
      // https://cloud.tencent.com/document/product/1110/36841#pzcs
      }
      try {
      const captcha = new window.TencentCaptcha(this.CaptchaAppId, this.callback, options)
      // 调用方法，显示验证码
      captcha.show()
      } catch (error) {
      console.log('js加载错误')
      }
    },
    async getValidCode() {
      if (this.validActive) return;

      if (!validPhone(this.phoneNumber)) {
        this.$toast("手机号有误");
        return;
      }
      await this.getTicket()
    },
    // 获取验证码
    // async getValidCode() {
    //   if (this.validActive) return;

    //   if (!validPhone(this.phoneNumber)) {
    //     this.$toast("手机号有误");
    //     return;
    //   }

    //   if (window.ic) {
    //     console.log(window.ic);
    //     window.ic.reset();
    //   }
    //   this.type = "get";
    //   // this.$toast("请滑动滑块发送验证码");
    //   this.aliSlideValide();
    // },
    // 滑块验证
    // aliSlideValide() {
    //   if (window.ic) return;

    //   this.showSlideDom = true;
    //   this.$nextTick(() => {
    //     const width = this.$refs.ncWrap.clientWidth;

    //     // nc超出的问题
    //     document.getElementById("nc").style.width = width + "px";

    //     // 当小屏幕的手机 宽度小于300时。阿里滑块不支持300px以下。这里结合tansform来做宽度适配
    //     // prefixStyle是自定义的工具函数，给transform加上浏览器的前缀
    //     if (width < 300) {
    //       this.$refs.ncWrap.style[prefixStyle("transform")] = `
    //         scale(${width / 300})
    //         translateX(-${(300 - width) / 2}px)
    //       `;
    //     }
    //     // 给阿里滑块验证做封装的函数
    //     slideValid({
    //       width,
    //       success: (data) => {
    //         this.validActive = true;
    //         this.countDown();

    //         const { sessionId, sig, token } = data;
    //         const { projectCode, projectId } = this.$store.state.projectData;

    //         if (this.type === "get") {
    //           const slideParams = {
    //             sessionId,
    //             sig,
    //             token,
    //             scene: configs.scene,
    //             ip: configs.ip,
    //             projectCode,
    //             clientSource: configs.source,
    //             source: configs.source,
    //             phoneNumber: this.phoneNumber,
    //             smsTemplateJson: "",
    //             projectId,
    //           };

    //           this.$api.user.sendMessage(slideParams, window.location.host);

    //           // this.$api.user.slideValid(slideParams)
    //         } else {
    //           const removeErrParams = {
    //             projectCode,
    //             mobile: this.phoneNumber,
    //             requestSource: configs.source,
    //             sessionId,
    //             sig,
    //             token,
    //             scene: configs.scene,
    //             ip: configs.ip,
    //             projectId,
    //           };
    //           this.$api.user.removeLoginError(removeErrParams).then((res) => {
    //             this.getText = "重新发送";
    //             this.initCountDown();
    //           });
    //         }
    //       },
    //       fail: (err) => {
    //         // 当阿里滑块验证出错时（比如频繁调用时），reload当前页面，为了防止用户再次输入手机号造成不好的体验，从localStorage中读取
    //         console.log("fail", err);
    //         window.localStorage.setItem("yxd2.0_ali_slide", this.phoneNumber);
    //         window.location.reload();
    //       },
    //       error: (err) => {
    //         // 当阿里滑块验证出错时（比如频繁调用时），reload当前页面，为了防止用户再次输入手机号造成不好的体验，从localStorage中读取
    //         console.log("error", err);
    //         window.localStorage.setItem("yxd2.0_ali_slide", this.phoneNumber);
    //         window.location.reload();
    //       },
    //     });
    //   });
    // },
    // 倒计时
    countDown() {
      if (this.timer) return;
      this.timer = setInterval(() => {
        if (this.validBtnSecond <= 1) {
          clearInterval(this.timer);
          this.timer = null;
          this.validActive = false;
          this.validBtnSecond = 60;
          return;
        }
        this.validBtnSecond--;
      }, 1000);
    },
    initCountDown() {
      clearInterval(this.timer);
      this.timer = null;
      this.validActive = false;
      this.validBtnSecond = 60;
    },
    async wxSignLogin(url) {
      try {
        let urlData = Object.fromEntries(
          window.location.search
            .slice(1)
            .split("&")
            .map((item) => item.split("="))
        );
        this.code = urlData.code;
        this.state = urlData.state;
        if (this.code && !this.globalIsLogin()) {
          const res = await this.$api.user.getWxLogin(this.code, this.state);
          // this.logText = res
          if (res.status === 200) {
            if (Object.keys(res.data).length === 1) {
              // 绑定手机号授权
              this.$store.dispatch("user/SET_AUTH", res.data.sign);
              // 隐藏加载提示
              if (this.showWxAuthLoading !== undefined) {
                this.showWxAuthLoading = false;
              }
              this.$router.push("/Wxauthorization");
            } else {
              // 用户已授权，登录操作
              // 设置用户信息
              this.$store.commit("user/SET_USERINFO", res.data);
              // 存储token
              this.$store.commit("user/SET_TOKEN", res.data.token.accessToken);
              setUserInfo(this.$cookies, res.data);
              setToken(this.$cookies, res.data.token.accessToken);
              // 重置埋点信息
              const appId = this.$store.state.projectData.projectId;
              const channel =
                this.$store.state.platform === "m" ? "yxd_h5" : "yxd_pc";
              const id = this.$store.state.user.userinfo.userId;
              const token = this.$store.state.user.token;
              window.MsStatis.init(appId, channel, id, token);
              console.log(this.$store.state.user.userinfo);
              if (this.$store.state.user.userinfo.integralNum) {
                this.$toast(
                  `恭喜您，获得${this.$store.state.user.userinfo.integralNum}梅花！`
                );
              }
              // 隐藏加载提示
              if (this.showWxAuthLoading !== undefined) {
                this.showWxAuthLoading = false;
              }
              if (
                this.$store.state.projectData.defaultHome &&
                this.$store.state.projectData.defaultHome === "/defaultHome"
              ) {
                this.$router.push("/defaultHome");
              } else {
                this.$router.push({
                  path: url || "/",
                  query: this.otherQuery,
                });
              }
            }
          } else {
            console.log(res.message);
            // 登录失败时也要隐藏加载提示
            if (this.showWxAuthLoading !== undefined) {
              this.showWxAuthLoading = false;
            }
          }
        } else {
          // 没有code或已登录时隐藏加载提示
          if (this.showWxAuthLoading !== undefined) {
            this.showWxAuthLoading = false;
          }
        }
      } catch (error) {
        console.error('微信登录出错:', error);
        // 出错时也要隐藏加载提示
        if (this.showWxAuthLoading !== undefined) {
          this.showWxAuthLoading = false;
        }
      }
    },
     async wxAuthorization(url) {

    //   wx.config({
    //     // 配置项
    //     debug: false,
    //     appId: 'your_app_id',
    //     timestamp: 1234567890,
    //     nonceStr: 'your_nonce_str',
    //     signature: 'your_signature',
    //     jsApiList: ['login']
    // });
    // wx.ready(function () {
    //     // 配置完成后调用该方法
    // });
      const res = await this.$api.user.getWxConfig();
      // console.log(300, res, this.$store.state.projectData.disparkState);
      // res.data = { appid: "wxf78cb6a7697c0596" };




      // 临时处理跳转到h5登录
      if (
        res.data &&
        (this.$store.state.projectData.disparkState === 1 ||
          this.$store.state.projectData.disparkState === 2)
      ) {
        this.appid = res.data.appid;
        // console.log(this.appid);
        this.$router.push(`/login?redirect=${url}`); 
      } else {
        this.$router.push(`/login?redirect=${url}`);
      }  
      


      // 微信环境微信授权登录
      // if (
      //   res.data &&
      //   (this.$store.state.projectData.disparkState === 1 ||
      //     this.$store.state.projectData.disparkState === 2)
      // ) {
      //   this.appid = res.data.appid;
      //   // console.log(this.appid);
      //   window.location.href =
      //     "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
      //     this.appid +
      //     "&redirect_uri=" +
      //     encodeURIComponent(window.location.origin + url) +
      //     "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
      //     console.log(window.location.href);
      // } else {
      //   this.$router.push(`/login?redirect=${url}`);
      // }  
    },
  },
};
