# 我的邀请用户页面API重复调用优化

## 问题描述

在 `/personal-center/my-invitation-users` 页面中，`yxd/web/medsciSurvey/getUserCompletedSurveys` 接口被重复调用了2次，导致不必要的网络请求和性能浪费。

## 问题原因分析

### 重复调用的根本原因

1. **移动端页面调用**：`pages/personal-center/my-invitation-users.vue` 在 `mounted` 钩子中调用了一次接口
2. **PC端组件调用**：`components/pc/user/PcMyInvitationUsers.vue` 在以下情况下也会调用接口：
   - `mounted` 钩子中调用一次
   - `watch` 监听器中的 `immediate: true` 导致组件初始化时再次调用

### 调用时序分析

```
页面加载 → 移动端/PC端组件同时初始化
    ↓
移动端 mounted() → 调用 getUserCompletedSurveys (第1次)
    ↓
PC端 mounted() → 调用 getUserCompletedSurveys (第2次)
    ↓
PC端 watch userId (immediate: true) → 可能再次调用 (潜在第3次)
```

## 优化方案

### 1. 统一数据管理策略

将数据加载逻辑统一到父组件 (`pages/personal-center/my-invitation-users.vue`) 中管理，子组件通过 props 接收数据。

### 2. 具体优化措施

#### 父组件优化 (`pages/personal-center/my-invitation-users.vue`)

1. **添加数据加载标记**：
   ```javascript
   data() {
     return {
       // ...
       dataLoaded: false // 防止重复加载
     };
   }
   ```

2. **统一数据加载**：
   ```javascript
   mounted() {
     this.userId = this.$route.query.userId || '';
     // 统一在父组件加载数据，避免重复调用
     this.loadUsersList();
   }
   ```

3. **传递数据给子组件**：
   ```vue
   <PcMyInvitationUsers
     :user-id="userId"
     :user-name="userName"
     :users-list="usersList"
     :loading="loading"
     @go-back="goBack"
     @load-data="loadUsersList"
   />
   ```

#### 子组件优化 (`components/pc/user/PcMyInvitationUsers.vue`)

1. **移除本地数据管理**：
   ```javascript
   props: {
     // 新增props接收父组件数据
     usersList: {
       type: Array,
       default: () => []
     },
     loading: {
       type: Boolean,
       default: false
     }
   }
   ```

2. **使用计算属性**：
   ```javascript
   computed: {
     list() {
       return this.usersList; // 使用父组件传递的数据
     }
   }
   ```

3. **移除重复的API调用**：
   - 删除 `getList()` 方法
   - 移除 `watch` 监听器
   - 简化 `mounted` 钩子

## 进一步优化 (PC端重复调用修复)

### 发现的问题
在初次优化后，发现PC端仍然存在重复调用的问题：
1. 父组件在 `mounted` 时调用一次 API
2. PC端子组件在 `mounted` 时又触发一次 `@load-data` 事件

### 最终解决方案
1. **完全移除子组件的数据加载逻辑**：PC端组件不再主动触发数据加载
2. **使用 `nextTick` 优化时序**：确保所有组件挂载完成后再加载数据
3. **移除不必要的事件监听**：删除 `@load-data` 事件绑定

### 关键修改
```javascript
// 父组件 - 优化挂载时序
async mounted() {
  this.userId = this.$route.query.userId || '';
  await this.$nextTick(); // 确保子组件挂载完成
  if (this.userId && !this.dataLoaded) {
    this.loadUsersList();
  }
}

// PC端子组件 - 移除数据加载逻辑
mounted() {
  // 完全依赖父组件管理数据
}
```

## 优化效果

### 性能提升

1. **减少网络请求**：从2次减少到1次，减少50%的API调用
2. **提升加载速度**：避免重复请求，页面响应更快
3. **降低服务器压力**：减少不必要的后端处理
4. **消除竞态条件**：避免多个组件同时请求导致的数据不一致

### 代码质量提升

1. **数据流清晰**：父组件统一管理数据，子组件专注展示
2. **避免竞态条件**：消除多个组件同时请求同一接口的问题
3. **更好的可维护性**：数据逻辑集中，便于调试和维护

### 用户体验改善

1. **减少加载闪烁**：避免多次数据更新导致的界面闪烁
2. **一致的加载状态**：统一的loading状态管理
3. **更快的响应时间**：减少网络请求延迟

## 验证方法

1. **功能验证**：
   - 验证移动端和PC端数据显示正常
   - 确认点击调研项目跳转功能正常
   - 测试返回按钮功能

2. **性能验证**：
   - 使用浏览器开发者工具的 Network 面板
   - 访问 `/personal-center/my-invitation-users` 页面
   - 确认 `getUserCompletedSurveys` 接口只被调用一次

3. **兼容性验证**：
   - 测试不同屏幕尺寸下的响应式切换
   - 验证移动端和PC端的数据同步

## 后续优化建议

1. **缓存机制**：考虑添加数据缓存，避免页面刷新时重复请求
2. **错误处理**：统一错误处理逻辑，提供更好的用户反馈
3. **加载优化**：考虑添加骨架屏或更友好的加载提示

## 相关文件

- `pages/personal-center/my-invitation-users.vue` - 主页面组件
- `components/pc/user/PcMyInvitationUsers.vue` - PC端子组件
- `api/modules/personalCenter.js` - API接口定义
