<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知情同意书勾选框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .consent-check-wrapper {
            margin-top: 20px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        .consent-checkbox {
            width: 16px;
            height: 16px;
            border: 1px solid #ddd;
            border-radius: 2px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            margin-top: 2px;
        }
        .consent-checkbox.checked {
            background: #2F92EE;
            border-color: #2F92EE;
            color: white;
        }
        .consent-text {
            font-size: 12px;
            color: #999;
            line-height: 16px;
        }
        .consent-link {
            color: #8A4CFF;
            text-decoration: underline;
            cursor: pointer;
        }
        .consent-link:hover {
            color: #1e7bd8;
        }
        .confirm-btn {
            width: 100%;
            height: 40px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 16px;
            border: none;
            background: #2F92EE;
            color: white;
            cursor: pointer;
        }
        .confirm-btn:disabled {
            background: #eee;
            color: #ccc;
            cursor: not-allowed;
        }
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        .dialog-overlay.show {
            display: flex;
        }
        .dialog {
            background: white;
            border-radius: 8px;
            max-width: 90%;
            max-height: 80%;
            display: flex;
            flex-direction: column;
        }
        .dialog-header {
            padding: 20px 16px 10px;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        .dialog-content {
            max-height: 60vh;
            overflow-y: auto;
            padding: 0 16px;
            flex: 1;
        }
        .consent-content {
            font-size: 14px;
            line-height: 22px;
            color: #666;
            text-align: justify;
            padding: 16px 0;
        }
        .consent-content h1, .consent-content h2, .consent-content h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 10px 0 5px 0;
        }
        .consent-content p {
            margin-bottom: 8px;
        }
        .dialog-footer {
            padding: 10px 16px 20px;
            border-top: 1px solid #eee;
        }
        .dialog-confirm-btn {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 6px;
            background: #8A4CFF;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }
        .dialog-confirm-btn:hover {
            background: #7a3ee6;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✓";
            color: #8A4CFF;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>微信授权页面 - 知情同意书功能测试</h2>
        
        <div class="test-section">
            <h3>页面布局预览</h3>
            <p>模拟 /Wxauthorization 页面的布局：</p>
            
            <!-- 模拟手机号输入 -->
            <div style="border: 1px solid #ddd; border-radius: 6px; padding: 10px; margin: 20px 0;">
                📱 请输入手机号
            </div>
            
            <!-- 模拟验证码输入 -->
            <div style="display: flex; gap: 10px; margin: 20px 0;">
                <div style="border: 1px solid #ddd; border-radius: 6px; padding: 10px; flex: 1;">
                    🔢 请输入验证码
                </div>
                <button style="padding: 10px 20px; border: 1px solid #8A4CFF; color: #8A4CFF; background: white; border-radius: 6px;">
                    发送验证码
                </button>
            </div>
            
            <!-- 知情同意书勾选框 -->
            <div class="consent-check-wrapper">
                <div class="consent-checkbox" id="consentCheckbox" onclick="toggleConsentAndCallAPI()">
                    <span id="checkMark" style="display: none;">✓</span>
                </div>
                <div class="consent-text">
                    我已阅读并同意
                    <span class="consent-link" onclick="showDialog()">《知情同意书》</span>
                </div>
            </div>

            <!-- 确认按钮 -->
            <button class="confirm-btn" id="confirmBtn" disabled>
                确 认
            </button>
        </div>
        
        <div class="test-section">
            <h3>功能特点</h3>
            <ul class="feature-list">
                <li>改为勾选框形式："我已阅读并同意《知情同意书》"</li>
                <li>必须勾选同意才能点击确认按钮</li>
                <li>点击《知情同意书》链接弹出内容弹窗</li>
                <li><strong>勾选框被选中时调用updateAgreeStatus接口</strong></li>
                <li>弹窗中点击同意只是关闭弹窗和勾选框</li>
                <li>只有当consentText不为空时才显示勾选框</li>
            </ul>
        </div>
    </div>
    
    <!-- 知情同意书弹窗 -->
    <div class="dialog-overlay" id="dialogOverlay" onclick="hideDialog()">
        <div class="dialog" onclick="event.stopPropagation()">
            <div class="dialog-header">
                知情同意书
            </div>
            <div class="dialog-content">
                <div class="consent-content" id="consentContent">
                    <h2>项目须知</h2>
                    <p>欢迎您参与本项目。在您开始使用我们的服务之前，请仔细阅读以下条款和条件。</p>
                    
                    <h3>1. 服务说明</h3>
                    <p>本平台为医疗健康信息服务平台，旨在为用户提供专业的医疗健康信息和服务。我们致力于为您提供准确、及时、有用的健康信息。</p>
                    
                    <h3>2. 用户权利</h3>
                    <p>作为用户，您有权：</p>
                    <p>• 获得准确、完整的服务信息</p>
                    <p>• 保护个人隐私和数据安全</p>
                    <p>• 随时终止使用我们的服务</p>
                    
                    <h3>3. 用户义务</h3>
                    <p>使用我们的服务时，您需要：</p>
                    <p>• 提供真实、准确的个人信息</p>
                    <p>• 遵守相关法律法规</p>
                    <p>• 不得滥用平台服务</p>
                    
                    <h3>4. 隐私保护</h3>
                    <p>我们严格保护您的个人隐私，不会未经授权向第三方披露您的个人信息。详细的隐私政策请参考我们的隐私条款。</p>
                    
                    <h3>5. 免责声明</h3>
                    <p>本平台提供的信息仅供参考，不能替代专业医疗建议。如有健康问题，请及时咨询专业医生。</p>
                    
                    <p style="margin-top: 20px; font-weight: bold;">
                        点击"我已阅读并同意"表示您已充分理解并同意遵守以上条款。
                    </p>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="dialog-confirm-btn" onclick="agreeConsent()">
                    我已阅读并同意
                </button>
            </div>
        </div>
    </div>
    
    <script>
        let isConsentChecked = false;

        function showDialog() {
            document.getElementById('dialogOverlay').classList.add('show');
        }

        function hideDialog() {
            document.getElementById('dialogOverlay').classList.remove('show');
        }

        function toggleConsentAndCallAPI() {
            isConsentChecked = !isConsentChecked;
            const checkbox = document.getElementById('consentCheckbox');
            const checkMark = document.getElementById('checkMark');
            const confirmBtn = document.getElementById('confirmBtn');

            if (isConsentChecked) {
                // 模拟调用 updateAgreeStatus 接口
                console.log('勾选时调用 updateAgreeStatus 接口，参数：', {
                    status: 1,
                    companyId: 'mock_company_id'
                });

                checkbox.classList.add('checked');
                checkMark.style.display = 'block';
                confirmBtn.disabled = false;
                confirmBtn.style.background = '#2F92EE';
                confirmBtn.style.cursor = 'pointer';

                // 显示成功提示
                setTimeout(() => {
                    alert('已确认知情同意书（接口调用成功）');
                }, 100);
            } else {
                checkbox.classList.remove('checked');
                checkMark.style.display = 'none';
                confirmBtn.disabled = true;
                confirmBtn.style.background = '#eee';
                confirmBtn.style.cursor = 'not-allowed';
            }
        }

        function agreeConsent() {
            // 弹窗中的同意按钮只是关闭弹窗和勾选框，不调用接口
            console.log('弹窗同意按钮：只关闭弹窗和勾选框，不调用接口');

            // 自动勾选同意框（这会触发接口调用）
            if (!isConsentChecked) {
                isConsentChecked = true;
                toggleConsentAndCallAPI();
            }

            // 关闭弹窗
            hideDialog();

            // 显示提示
            alert('请勾选同意框完成确认');
        }

        // 阻止弹窗内容区域的点击事件冒泡
        document.querySelector('.dialog').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
