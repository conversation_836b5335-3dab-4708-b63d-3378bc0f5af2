import {
  getToken,
  getMedsciToken,
  getUserInfo,
  setToken,
  setUserInfo,
  removeToken,
  removeUserInfo
} from "@/utils/auth";
import Vue from "vue";

export default async ({ app, redirect, store, req }) => {
  if (process.client) {
    const userInfo = getUserInfo(app.$cookies);
    const token = getToken(app.$cookies);

    if (userInfo) {
      store.commit("user/SET_USERINFO", userInfo);
    } else {
      removeToken(app.$cookies);
    }

    if (token) {
      store.commit("user/SET_TOKEN", token);
    } else {
      removeUserInfo(app.$cookies);
    }
  }
  let host = "";
  if (process.server) {
    host = req.headers.host;
  } else {
    host = window.location.host;
  }
  let projectData;
  try {
    projectData = await store.dispatch("getProjectIdAndCode", host);
    // 浏览器标签页的标题前缀
    Vue.prototype.globalTitle = projectData?.name;
  } catch (e) {
    console.error('获取项目信息失败:', e);
    projectData = null
  }

  let whiteList = ["/login", "/privacy-policy", "/information", "/end", "/Wxauthorization"];

  // let whiteList = ['/login', '/privacy-policy', '/information'] // no redirect whitelist
  app.router.beforeEach(async (to, from, next) => {
    // 清理可能残留的模态框遮罩层（特别是从外部站点跳转时）
    if (process.client) {
      try {
        // 清理Element UI的模态框遮罩层
        const elModalOverlays = document.querySelectorAll('.v-modal');
        if (elModalOverlays && elModalOverlays.length > 0) {
          elModalOverlays.forEach(overlay => {
            if (overlay && overlay.parentNode) {
              overlay.parentNode.removeChild(overlay);
            }
          });
        }

        // 重置body的overflow样式，防止页面滚动被锁定
        if (document.body) {
          document.body.style.overflow = '';
          document.body.style.paddingRight = '';
          document.body.classList.remove('el-popup-parent--hidden');
        }
      } catch (error) {
        // 静默处理错误，不影响正常流程
      }
    }

    // 判断是否进入项目结束页面了  如果是  就不执行下边的方法  如果不是就按照原来的来
    if (to.path == "/end") {
      // 如果成功访问/end页面，清除跳转标记
      if (process.client) {
        sessionStorage.removeItem('endRedirectAttempt');
      }
      next(); // 提前结束后续导航守卫函数的执行
      return;
    } else {
      if (process.client) {
        // 检查项目数据，如果获取失败且不在/end页面，则跳转到/end
        // 添加防死循环保护：检查是否已经尝试过跳转
        if (!projectData && location.href.indexOf("/end") == -1) {
          // 添加防死循环机制：检查sessionStorage中的跳转标记
          const endRedirectAttempt = sessionStorage.getItem('endRedirectAttempt');
          const currentTime = Date.now();

          // 如果5分钟内已经尝试过跳转，则不再跳转，避免死循环
          if (!endRedirectAttempt || (currentTime - parseInt(endRedirectAttempt)) > 5 * 60 * 1000) {
            sessionStorage.setItem('endRedirectAttempt', currentTime.toString());
            console.warn('项目数据获取失败，跳转到结束页面');
            window.location.replace(location.origin + "/end");
            return false;
          } else {
            console.warn('检测到可能的死循环，跳过/end跳转');
            // 清除标记，允许正常访问其他页面
            sessionStorage.removeItem('endRedirectAttempt');
          }
        }

        // 如果有项目数据，清除之前的跳转标记，允许正常访问
        if (projectData) {
          sessionStorage.removeItem('endRedirectAttempt');
        }
      }
      let isWeixin;
      // sourceId 第三方用户id标识，用于记录用户行为埋点
      if (to.query && to.query.sourceId) {
        if (process.client) {
          localStorage.setItem("sourceId", to.query.sourceId);
          store.commit("SET_SOURCEID", to.query.sourceId);
        }
      }
      if (to.query && to.query.isWeixin) {
        isWeixin = to.query.isWeixin;
        if (process.client) {
          localStorage.setItem("isWeixin", isWeixin);
          store.commit("SET_ISWEIXIN", isWeixin);
        }
      }

      if (to.query?.inviteUserId && to.query?.inviteAccountType) {
        const { inviteUserId, inviteAccountType } = to.query;
        if (process.client) {
          localStorage.setItem("inviteUserId", inviteUserId);
          localStorage.setItem("inviteAccountType", inviteAccountType);
        }
      }
      if (to.query?.page && process.client) {
        localStorage.setItem("page", to.query.page);
      }
      if (to.query?.isOne && process.client) {
        localStorage.setItem("isOne", to.query.isOne);
      }
      if (process.client) {
        if (localStorage.getItem("isWeixin") && localStorage.getItem("isWeixin") == "weixin") {
          isWeixin = localStorage.getItem("isWeixin")
        }
      }
      if (to.query && to.query.fenxiang) {
        if (process.client) {
          localStorage.setItem("fenxiang", to.query.fenxiang);
          store.commit("SET_FENXIANG", to.query.fenxiang);
        }
      }
      const userInfo = to.query?.userInfo ||
        (getUserInfo(app.$cookies) ? JSON.stringify(getUserInfo(app.$cookies)) : null);
      let accessToken;
      if (userInfo) {
        try {
          const parsedUserInfo = JSON.parse(userInfo);
          accessToken = parsedUserInfo.token?.accessToken;
          if (accessToken) {
            store.commit("user/SET_TOKEN", accessToken);
            store.commit("user/SET_USERINFO", parsedUserInfo);
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
      if (store.state.isWeixin == "weixin" || isWeixin == "weixin") {
        // 微信环境处理逻辑
        if (userInfo && process.client) {
          let parsedUserInfo;
          try {
            parsedUserInfo = JSON.parse(userInfo);
            setUserInfo(app.$cookies, parsedUserInfo);
            setToken(app.$cookies, accessToken);
          } catch (parseError) {
            console.error('解析用户信息失败:', parseError);
            await store.dispatch("user/logout");
            wx.miniProgram.navigateTo({
              url: "/pages/auth_mobile/index",
            });
            return;
          }

          const accountType = parsedUserInfo.accountType;
          if (typeof accountType == 'undefined') {
            // 旧用户没有值时退出登录
            await store.dispatch("user/logout");
            wx.miniProgram.navigateTo({
              url: "/pages/auth_mobile/index",
            });
            return; // 提前返回，避免继续执行
          }

          // 获取各种状态
          let agreeStatus, infoStatus, authStatus;
          try {
            // 并行获取状态信息以提高性能
            const [agreeResult, infoResult, authResult] = await Promise.allSettled([
              store.dispatch("user/getAgreeBook"),
              store.dispatch("user/getInfoStatus"),
              store.dispatch("user/getAuthStatus")
            ]);

            // 设置更安全的默认值
            agreeStatus = agreeResult.status === 'fulfilled' ? agreeResult.value : 0;
            infoStatus = infoResult.status === 'fulfilled' ? infoResult.value : { data: true }; // API失败时默认为已完善
            authStatus = authResult.status === 'fulfilled' ? authResult.value : { data: 4 }; // API失败时默认为未开启认证
          } catch (e) {
            // 任何API失败都返回登录页
            console.error('获取用户状态失败:', e);
            await store.dispatch("user/logout");
            wx.miniProgram.navigateTo({
              url: "/pages/auth_mobile/index",
            });
            return; // 提前返回，避免继续执行
          }

          // 根据用户类型进行权限检查
          if (accountType === 0) {
            // 医生用户：项目须知 → 完善信息 → 认证
            if (agreeStatus !== 1) {
              if (to.path !== "/informed-consent") {
                next({ path: "/informed-consent" });
                return;
              } else {
                next();
              }
            } else if (infoStatus.data !== true) {
              if (to.path !== "/privacy-policy") {
                next({ path: `/privacy-policy?redirect=${to.fullPath}` });
                return;
              } else {
                next();
              }
            } else if (authStatus.data == 0 || authStatus.data == 1 || authStatus.data == 3) {
              if (to.path !== "/authentication") {
                next({ path: `/authentication?authStatus=${authStatus.data}&redirect=${to.fullPath}` });
                return;
              } else {
                next();
              }
            } else {
              next();
            }
          } else if (accountType === 1) {
            // 患者用户：跳过项目须知，直接完善信息
            if (infoStatus.data !== true) {
              if (process.client && localStorage.getItem('accountTypePop')) {
                localStorage.removeItem("initImage");
              } else {
                const imageValue = infoStatus.image || 'noimage';
                if (process.client) {
                  localStorage.setItem("initImage", imageValue);
                }
              }
            }
          }
          next();
          return; // 微信环境处理完毕，提前返回
        }

        // 处理未登录的微信用户
        if (!userInfo) {
          if (process.client && getUserInfo(app.$cookies) && to.query.out) {
            await store.dispatch('user/logout');
          }

          if (to.path === "/login" || to.path === "/information") {
            // 构建跳转URL，使用to.path而不是from.path避免循环
            let queryString = "";
            for (const key in to.query) {
              queryString += `${key}=${to.query[key]}&`;
            }
            queryString = queryString.slice(0, -1); // 移除最后的&

            const redirectUrl = queryString
              ? `${window.location.origin}${to.path}?${queryString}`
              : `${window.location.origin}${to.path}`;

            wx.miniProgram.navigateTo({
              url: `/pages/auth_mobile/index?path=${encodeURIComponent(redirectUrl)}`
            });

            // 跳转到首页而不是from.path，避免循环
            next({
              path: "/",
              replace: true,
            });
            return;
          }
        }

        // 检查白名单和项目状态
        let disparkState = 0;
        try {
          disparkState = await store.dispatch("getDisparkState");
        } catch (err) {
          console.log('获取项目状态失败:', err);
        }

        if (whiteList.indexOf(to.path) !== -1 || disparkState == 1) {
          if (to.path === "/personal-center" && !getUserInfo(app.$cookies)) {
            wx.miniProgram.navigateTo({
              url: "/pages/auth_mobile/index",
            });
            next({
              path: from.path,
              query: from.query,
              replace: true,
            });
            return;
          }
          next();
        } else {
          if (!getUserInfo(app.$cookies) && to.path !== "/") {
            wx.miniProgram.navigateTo({
              url: "/pages/auth_mobile/index",
            });
            next({
              path: from.path,
              query: from.query,
              replace: true,
            });
            return;
          }
          next();
        }
      } else {
        const hasMedsciToken = getMedsciToken(app.$cookies);
        // const hasToken = getToken(app.$cookies);
        const hasToken = getUserInfo(app.$cookies)?.token?.accessToken;

        if (hasToken) {
          // 如果cookie中存在token且store中不存在， 则把token存储到store中
          if (!store.state.user.token) store.commit("user/SET_TOKEN", hasToken);

          const userInfo = getUserInfo(app.$cookies)
            ? getUserInfo(app.$cookies)
            : {};
          store.commit("user/SET_USERINFO", userInfo);

          // 判断是否有userId, 没有则去获取
          const userId = store.state.user.userinfo.userId;
          // 判断当前登录用户类型 医生还是患者
          const accountType = store.state.user.userinfo.accountType;
          if (!userId || typeof accountType == 'undefined') {
            await store.dispatch("user/logout");
            redirect("/login");
          }

          // 获取各种状态信息
          let agreeStatus, infoStatus, authStatus;
          try {
            // 并行获取状态信息以提高性能
            const [agreeResult, infoResult, authResult] = await Promise.allSettled([
              store.dispatch("user/getAgreeBook"),
              store.dispatch("user/getInfoStatus"),
              store.dispatch("user/getAuthStatus")
            ]);

            // 设置更安全的默认值
            agreeStatus = agreeResult.status === 'fulfilled' ? agreeResult.value : 0;
            infoStatus = infoResult.status === 'fulfilled' ? infoResult.value : { data: true }; // API失败时默认为已完善
            authStatus = authResult.status === 'fulfilled' ? authResult.value : { data: 4 }; // API失败时默认为未开启认证
          } catch (e) {
            // 任何API失败都返回登录页
            console.error('获取用户状态失败:', e);
            await store.dispatch("user/logout");
            redirect("/login");
            return;
          }

          // 根据用户类型进行不同的权限检查逻辑
          if (accountType === 0) {
            // 医生用户：需要先检查项目须知，再检查信息完善，最后检查认证
            if (agreeStatus !== 1) {
              if (to.path !== "/informed-consent") {
                next({ path: "/informed-consent" });
                return;
              } else {
                next();
              }
            } else if (infoStatus.data !== true) {
              if (to.path !== "/privacy-policy") {
                next({ path: `/privacy-policy?redirect=${to.fullPath}` });
                return;
              } else {
                next();
              }
            } else if(authStatus.data == 0 || authStatus.data == 1 || authStatus.data == 3) {
              // 0-未上传 1-待审核  2-审核通过  3-审核不通过  4-项目未开启认证
              if (to.path !== "/authentication") {
                next({ path: `/authentication?redirect=${to.fullPath}&authStatus=${authStatus.data}` });
                return;
              } else {
                next();
              }
            } else {
              if (to.path === "/login") {
                next({ path: "/" });
              } else {
                next();
              }
            }
          } else if (accountType === 1) {
            // 患者用户：跳过项目须知，直接进入完善信息逻辑
            if (infoStatus.data !== true) {
              if (process.client && localStorage.getItem('accountTypePop')) {
                localStorage.removeItem("initImage");
              } else {
                const imageValue = infoStatus.image || 'noimage';
                if (process.client) {
                  localStorage.setItem("initImage", imageValue);
                }
              }
            }
            if (to.path === "/login") {
              next({ path: "/" });
            } else {
              next();
            }
          } else {
            if (to.path === "/login") {
              next({ path: "/" });
            } else {
              next();
            }
          }
        } else {
          // 一站式登录，主站有登录信息，apoc没有登录，走免登录逻辑
          // 只在客户端处理一站式登录，服务端跳过
          if (process.client && host !== 'apo-show.medsci.cn' && hasMedsciToken && whiteList.indexOf(to.path) === -1 && !to.query?.inviteUserId) {
            try {
              console.log('客户端开始一站式登录...');
              await store.dispatch("user/autoLogin", { medsci: hasMedsciToken });
              console.log('一站式登录完成');

              // 登录成功后，重新获取用户信息并更新store
              const newUserInfo = getUserInfo(app.$cookies);
              const newToken = getToken(app.$cookies);

              if (newUserInfo && newToken) {
                store.commit("user/SET_TOKEN", newToken);
                store.commit("user/SET_USERINFO", newUserInfo);

                console.log('一站式登录成功，强制刷新页面');
                // 使用window.location.href强制刷新页面，确保状态同步
                window.location.href = window.location.origin + to.fullPath;
                return;
              }
            } catch (e) {
              console.error('一站式登录失败:', e);
              // 登录失败，继续执行正常的登录逻辑
            }
          }

          // 检查一站式登录后的用户状态
          const currentUserInfo = getUserInfo(app.$cookies);
          const currentToken = currentUserInfo?.token?.accessToken;

          if (currentToken && currentUserInfo?.userId) {
            // 一站式登录成功，用户已登录，执行已登录用户的逻辑
            console.log('检测到用户已登录（可能通过一站式登录），执行已登录逻辑');

            // 设置store状态（如果还没设置的话）
            if (!store.state.user.token) {
              store.commit("user/SET_TOKEN", currentToken);
              store.commit("user/SET_USERINFO", currentUserInfo);
            }

            // 跳转到已登录用户的逻辑处理（重用上面的代码）
            const userId = currentUserInfo.userId;
            const accountType = currentUserInfo.accountType;

            if (!userId || typeof accountType == 'undefined') {
              console.warn('用户信息不完整，跳转到登录页');
              next({ path: '/login' });
              return;
            }

            // 这里应该执行与上面已登录用户相同的权限检查逻辑
            // 为了避免代码重复，我们直接允许访问，让页面组件自己处理权限
            if (to.path === "/login") {
              next({ path: "/" });
            } else {
              next();
            }
            return;
          }

          // 正常的未登录处理逻辑
          let disparkState = 0;
          try {
            disparkState = await store.dispatch("getDisparkState");
          } catch (err) {
            console.log(err);
          }

          if (whiteList.indexOf(to.path) !== -1 || disparkState == 1) {
            next();
          } else {
            // 检查projectData是否存在，避免访问null对象的属性
            const loginType = store.state.projectData?.loginType || 0;
            if (loginType === 1) {
              // 避免重定向循环：如果已经在信息页面，直接通过
              if (to.path === '/information') {
                next();
              } else if (to.fullPath.includes("redirect")) {
                next(`/information?redirect=${to.path}`);
              } else {
                next(`/information?redirect=${to.fullPath}`);
              }
            } else {
              // 避免重定向循环：如果已经在登录页面，直接通过
              if (to.path === '/login') {
                next();
              } else if (to.fullPath.includes("redirect")) {
                next(`/login?redirect=${to.path}`);
              } else {
                next(`/login?redirect=${to.fullPath}`);
              }
            }
          }
        }
      }
      // 设置页面根元素的fontSize属性
      // setRem()
    }

  });
};
